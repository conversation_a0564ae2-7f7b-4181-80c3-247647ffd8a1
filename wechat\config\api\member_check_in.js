import request from '@/utils/request'

// 查询入住会员列表
export const listMemberCheckin = (params) => {
  return request({
    url: '/api/memberCheckin/get',
    method: 'post',
    data: params
  })
}

export const addMemberInfo = (params) => {
    return request({
        url:'/api/memberCheckin/save',
        method: 'post',
		data: params
    })
}


export const updateMemberInfo = (params) => {
    return request({
        url:'/api/memberCheckin/update',
        method: 'post',
		data: params
    })
}

