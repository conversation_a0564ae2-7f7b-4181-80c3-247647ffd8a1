<template>
	<view class="my-container" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
		<!-- 用户信息区域 -->
		<view class="user-info-section">
			<view class="user-avatar">
				<image :src="userInfo.avatarUrl || '/static/logo.png'" mode="aspectFill"></image>
			</view>
			<view class="user-details" @click="handleLogin">
				<view class="user-name">{{userInfo.wxNickname || '点击登录'}}</view>
				<view class="user-phone">{{userInfo.phoneNumber || '未绑定手机号'}}</view>
			</view>
		</view>

		<!-- 功能菜单区域 -->
		<view class="menu-list">
			<view class="menu-item" @click="navigateTo('/pages_business/pages/maternal-info/maternal-info')" >
				<view class="menu-text">登记信息</view>
				<view class="menu-arrow">
					<text class="iconfont">&gt;</text>
				</view>
			</view>
			<view class="menu-item" @click="navigateTo('/pages_business/pages/my/info')">
				<view class="menu-text">个人信息</view>
				<view class="menu-arrow">
					<text class="iconfont">&gt;</text>
				</view>
			</view>
			<!--   -->
			<view class="menu-item" @click="navigateTo('/pages_business/pages/my/prize')" >
				<view class="menu-text">我的奖品</view>
				<view class="menu-arrow">
					<text class="iconfont">&gt;</text>
				</view>
			</view>
			<view class="menu-item" @click="navigateTo('/pages_business/pages/my/car')">
				<view class="menu-text">我的车辆</view>
				<view class="menu-arrow">
					<text class="iconfont">&gt;</text>
				</view>
			</view>
		</view>

		<!-- 退出登录按钮 -->
		<view class="menu-list" v-if="isLogin">
			<view class="menu-item" style="text-align: center;" @click="handleLogout">
				<view class="menu-text">退出登录</view>
			</view>
		</view>
		<!-- <button class="logout-btn"  ></button> -->
		
		<!-- 版本信息，固定在底部 -->
		<view class="version-info" :class="{'show': showVersionInfo}">
			<text>noname 技术支持</text>
		</view>
	</view>
</template>

<script>
	/**
	 * 个人中心页面
	 * 用于展示用户信息和提供个人相关功能入口
	 */
	import { API, request } from '@/config/api.js';

	export default {
		data() {
			return {
				// 用户信息
				userInfo: {
					avatarUrl: '',
					wxNickname: '',
					phoneNumber: '',
					referralCode: ''
				},
				// 登录状态
				isLogin: false,
				// 是否为开发环境
				isDev: process.env.NODE_ENV === 'development',
				// 控制版本信息显示
				showVersionInfo: false,
				// 触摸事件相关
				touchStartY: 0,
				touchMoveY: 0
			}
		},
		onLoad() {
			// 页面加载时检查登录状态
			this.checkLoginStatus();
		},
		onShow() {
			// 页面显示时检查登录状态，确保数据最新
			this.checkLoginStatus();
		},
		methods: {
			/**
			 * 处理触摸开始事件
			 */
			handleTouchStart(e) {
				this.touchStartY = e.touches[0].clientY;
			},
			
			/**
			 * 处理触摸移动事件
			 */
			handleTouchMove(e) {
				this.touchMoveY = e.touches[0].clientY;
				// 如果是向上滑动（结束位置Y坐标小于开始位置Y坐标）
				if (this.touchMoveY < this.touchStartY && (this.touchStartY - this.touchMoveY) > 30) {
					this.showVersionInfo = true;
				}
			},
			
			/**
			 * 处理触摸结束事件
			 */
			handleTouchEnd() {
				// 触摸结束时隐藏版本信息
				this.showVersionInfo = false;
			},

			/**
			 * 检查登录状态
			 * 获取用户信息
			 */
			checkLoginStatus() {
				const token = uni.getStorageSync('token');

				// 检查是否已登录
				if (token) {
					// 从本地存储获取用户信息
					const userInfoStr = uni.getStorageSync('userInfo');
					if (userInfoStr) {
						try {
							const userInfo = JSON.parse(userInfoStr);
							// 更新用户信息
							this.userInfo = {
								avatarUrl: userInfo.wxAvatarUrl || userInfo.avatar || userInfo.avatarUrl || '/static/logo.png',
								wxNickname: userInfo.wxNickname || userInfo.name || userInfo.wxNickname || (userInfo.id ? '用户' + String(userInfo.id).substr(-4) : '用户'),
								phoneNumber: userInfo.phoneNumber || userInfo.phoneNumber || '',
								referralCode: userInfo.referralCode || '',
								id: userInfo.id || ''
							};

							this.isLogin = true;

							// 同时从服务器获取最新用户信息
							// this.fetchUserInfo();
						} catch (error) {
							console.error('解析用户信息失败:', error);
							this.isLogin = false;

							// 清除无效的用户信息
							uni.removeStorageSync('userInfo');
							uni.removeStorageSync('token');
						}
					} else {
						// 本地没有用户信息，尝试从服务器获取
						// this.fetchUserInfo();
					}
				} else {
					this.isLogin = false;
					this.handleLogin()
				}
			},

			/**
			 * 从服务器获取用户信息
			 */
			fetchUserInfo() {
				const token = uni.getStorageSync('token');
				if (!token) return;

				// request({
				// 	url: API.appUser.info,
				// 	method: 'GET',
				// 	showError: false
				// }).then(res => {
				// 	// 更新用户信息
				// 	const userInfo = res;
				// 	this.userInfo = {
				// 		avatarUrl: userInfo.wxAvatarUrl || userInfo.avatar || userInfo.avatarUrl || '/static/logo.png',
				// 		wxNickname: userInfo.wxNickname || userInfo.name || userInfo.wxNickname || (userInfo.id ? '用户' + String(userInfo.id).substr(-4) : '用户'),
				// 		phoneNumber: userInfo.phoneNumber || userInfo.phoneNumber || '',
				// 		id: userInfo.id || ''
				// 	};

				// 	// 更新本地存储
				// 	uni.setStorageSync('userInfo', JSON.stringify(userInfo));

				// 	this.isLogin = true;
				// 	console.log('当前登录用户(服务器):', this.userInfo);
				// }).catch(error => {
				// 	console.error('获取用户信息失败:', error);

				// 	// 如果是未授权错误，清除登录信息
				// 	if (error.message === '未登录' || error.message === '未授权' || error.statusCode === 401) {
				// 		uni.removeStorageSync('userInfo');
				// 		uni.removeStorageSync('token');
				// 		this.isLogin = false;
				// 	}
				// });
			},

			/**
			 * 处理登录点击事件
			 * 未登录时跳转到登录页面
			 */
			handleLogin() {
				if (this.isLogin) return;

				// 跳转到登录页面
				uni.navigateTo({
					url: '/pages/login/login'
				});
			},

			/**
			 * 模拟登录成功
			 * 用于开发环境或演示
			 */
			mockLogin() {
				const mockUserInfo = {
					avatarUrl: '/static/logo.png',
					wxNickname: '测试用户',
					phoneNumber: '13800138000'
				};

				// 保存到本地缓存
				uni.setStorageSync('token', 'mock_token_123456');
				uni.setStorageSync('userInfo', JSON.stringify(mockUserInfo));

				// 更新状态
				this.userInfo = mockUserInfo;
				this.isLogin = true;

				uni.showToast({
					title: '登录成功',
					icon: 'success'
				});
			},

			/**
			 * 处理退出登录
			 */
			handleLogout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除本地存储
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							uni.removeStorageSync('memberInfo');

							// 重置状态
							this.userInfo = {
								avatarUrl: '',
								wxNickname: '',
								phoneNumber: '',
								referralCode: ''
							};
							this.isLogin = false;

							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							});
							
							setTimeout(() => {
								uni.reLaunch({
									url:'/pages/login/login'
								})
							},1000)
							
						}
					}
				});
			},

			/**
			 * 页面导航
			 * @param {String} url 目标页面路径
			 */
			navigateTo(url) {
				// 检查是否登录
				if (!this.isLogin) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					return;
				}

				// 导航到目标页面
				uni.navigateTo({
					url: url
				});
			},
		}
	}
</script>

<style>
	.my-container {
		padding: 100rpx 20rpx 20rpx;
		background-color: #FAFAF5;
		min-height: calc(100vh - 120rpx);
		position: relative;
	}

	/* 用户信息区域样式 */
	.user-info-section {
		display: flex;
		align-items: center;
		padding: 40rpx 30rpx;
		/* border-radius: 12rpx; */
		/* margin-bottom: 30rpx; */
		/* box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05); */
	}

	.user-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-right: 30rpx;
		border: 2rpx solid #eee;
	}

	.user-avatar image {
		width: 100%;
		height: 100%;
	}

	.user-details {
		flex: 1;
	}

	.user-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.user-phone {
		font-size: 26rpx;
		color: #999;
	}

	/* 菜单列表样式 */
	.menu-list {
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
		padding: 0 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
	}

	.menu-item:last-child {
		border-bottom: none;
	}

	.menu-icon {
		margin-right: 20rpx;
		font-size: 36rpx;
	}

	.menu-text {
		flex: 1;
		font-size: 28rpx;
		color: #333;
	}

	.menu-arrow {
		color: #ccc;
		font-size: 24rpx;
	}

	/* 退出登录按钮样式 */
	.logout-btn {
		width: 100%;
		margin: 30rpx auto;
		padding: 15rpx 0;
		background-color: #fff;
		color: #333;
		border-radius: 12rpx;
		font-size: 28rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		border-color: transparent;
	}

	/* 版本信息样式 */
	.version-info {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		text-align: center;
		color: #161616;
		font-size: 24rpx;
		padding: 16rpx 0;
		background-color: #FAFAF5;
		transform: translateY(100%);
		transition: transform 0.3s ease;
		z-index: 100;
	}

	/* 当showVersionInfo为true时显示版本信息 */
	.version-info.show {
		transform: translateY(0); /* 显示版本信息 */
	}

</style>