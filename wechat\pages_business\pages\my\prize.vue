<template>
  <view class="prize-container">
    <custom-nav-bar title="我的奖品" :showBack="isShowBack" :showBackInfo="!isShowBack"></custom-nav-bar>
    <!-- 加载状态 -->
    <view class="loading-section" v-if="loading">
      <view class="loading-text">正在加载...</view>
    </view>

    <!-- 无奖品状态 -->
    <view class="empty-section" v-else-if="!memberInfo.prizeContent">
      <view class="empty-icon">🎯</view>
      <view class="empty-title">暂无奖品</view>
      <view class="empty-desc">您还没有获得任何奖品，快去参与抽奖吧！</view>
      <button class="lottery-btn" @tap="goToLottery">
        前往抽奖
      </button>
    </view>

    <!-- 有奖品状态 -->
    <view class="prize-container-content" v-else>
      <view class="prize-header">
        <text class="prize-title">恭喜您获得奖品</text>
      </view>

      <!-- 转盘奖品展示 -->
      <view class="single-prize">
        <!-- <view class="prize-icon-large">
          🎁
        </view> -->
        <image class="prize-icon-large" :src="getLargeImageUrl('1952756108267401218')"></image>
        <view class="prize-details">
          <view class="prize-name-large">{{ memberInfo.prizeContent }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref, onMounted } from 'vue'
import {getLargeImageUrl} from '@/config/api/image.js';
import { listMemberCheckin } from "@/config/api/member_check_in.js";

// 响应式数据
const loading = ref(true)
const memberInfo = ref({})
const isShowBack = ref(true) 

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '未知'

  try {
    const date = new Date(timeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    console.error('时间格式化失败:', error)
    return '时间格式错误'
  }
}

onLoad((options) => {
  if(options.isShowBack) isShowBack.value = options.isShowBack === 'false' ? false : true
})

// 页面加载时获取用户信息
onMounted(() => {
  fetchMemberInfo()
})

// 获取用户信息
const fetchMemberInfo = async () => {
  try {
    loading.value = true
    console.log('开始获取用户奖品信息...')

    const response = await listMemberCheckin()
    console.log('用户信息获取结果:', response)

    if (response) {
      memberInfo.value = response
      console.log('用户奖品信息:', memberInfo.value.prizeContent)
    } else {
      console.warn('用户信息获取失败')
    }

  } catch (error) {
    console.error('获取用户信息失败:', error)
    uni.showToast({
      title: '获取奖品信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}



// 前往抽奖
const goToLottery = () => {
  console.log('前往抽奖页面')
  uni.navigateTo({
    url: '/pages_business/pages/turntable/turntable'
  })
}
</script>

<style lang="scss" scoped>
.prize-container {
  min-height: 100vh;
  background: #FAFAF5;
  padding: 40rpx 30rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  .loading-text {
    font-size: 32rpx;
    color: #666;
  }
}

/* 空状态样式 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 40rpx;
  }

  .empty-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .empty-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 60rpx;
  }
}

/* 抽奖按钮 */
.lottery-btn {
  width: 100%;
  height: 96rpx;
  background: #8b5a2b;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  border: none;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(52, 152, 219, 0.3);

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 6rpx 15rpx rgba(52, 152, 219, 0.3);
  }


}

/* 奖品容器 */
.prize-container-content {
  // background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  // box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.prize-header {
  text-align: center;
  margin-bottom: 60rpx;

  .prize-title {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
  }
}

/* 单个奖品展示 */
.single-prize {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 60rpx;
}

.prize-icon-large {
  width: 367rpx;
  height: 412rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.prize-details {
  width: 100%;

  .prize-name-large {
    font-size: 42rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .prize-desc-large {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 30rpx;
    line-height: 1.5;
  }

  .prize-time-large {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 20rpx;
  }

  .prize-status {
    font-size: 28rpx;
    color: #ff6b35;
    font-weight: 500;
    padding: 10rpx 20rpx;
    background: #fff5f0;
    border-radius: 20rpx;
    display: inline-block;
  }
}




</style>