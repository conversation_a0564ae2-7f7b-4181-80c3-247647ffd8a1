<template>
	<view class="tasting-container">
		<custom-nav-bar title="试餐预约"></custom-nav-bar>
		
		<view class="tasting-content">
			<!-- 我的试餐预约记录 -->
			<view class="history-section" v-if="reservationList.length > 0">
				<view class="section-title">我的试餐预约</view>
				<view class="reservation-list">
					<view
						class="reservation-item"
						v-for="(item, index) in reservationList"
						:key="item.id"
					>
						<view class="reservation-header">
							<view class="reservation-title">{{ item.title || '试餐预约' }}</view>
							<view class="reservation-status" :class="getStatusClass(item.status)">
								{{ getStatusText(item.status) }}
							</view>
						</view>
						<view class="reservation-info">
							<view class="info-row">
								<text class="info-label">预约时间：</text>
								<text class="info-value">{{ formatDate(item.reservationTime) }}</text>
							</view>
							<view class="info-row" v-if="item.extendedProperties">
								<text class="info-label">用餐时段：</text>
								<text class="info-value">{{ getExtendedProperty(item.extendedProperties, 'mealTime') || '未指定' }}</text>
							</view>
							<view class="info-row" v-if="item.extendedProperties">
								<text class="info-label">用餐人数：</text>
								<text class="info-value">{{ getExtendedProperty(item.extendedProperties, 'participantCount') || 1 }}人{{ getExtendedProperty(item.extendedProperties, 'participantCount') > 5 ? '以上' : '' }}</text>
							</view>
							<view class="info-row" v-if="getExtendedProperty(item.extendedProperties, 'remarks')">
								<text class="info-label">备注：</text>
								<text class="info-value">{{ getExtendedProperty(item.extendedProperties, 'remarks') }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 试餐介绍 -->
			<view class="intro-section">
				<view class="intro-title">{{ reservationList.length > 0 ? '新增试餐预约' : '试餐体验' }}</view>
				<view class="intro-desc">
					我们提供专业的月子餐试吃体验，让您提前感受我们精心准备的营养餐食。
					请选择您方便的日期和用餐时段，我们将为您安排专业的营养师介绍。
				</view>
			</view>

			<!-- 预约表单 -->
			<view class="form-section">
				<view class="form-title">预约信息</view>
				
				<view class="form-item">
					<view class="form-label">联系人姓名</view>
					<input 
						class="form-input" 
						v-model="formData.contactName" 
						placeholder="请输入联系人姓名"
						maxlength="20"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">联系电话</view>
					<input 
						class="form-input" 
						v-model="formData.contactPhone" 
						placeholder="请输入联系电话"
						type="number"
						maxlength="11"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">预约日期</view>
					<picker 
						mode="date" 
						:value="formData.reservationDate" 
						:start="minDate"
						:end="maxDate"
						@change="onDateChange"
					>
						<view class="form-input picker-input">
							{{ formData.reservationDate || '请选择预约日期' }}
						</view>
					</picker>
				</view>

				<view class="form-item">
					<view class="form-label">用餐时段</view>
					<picker
						:range="mealTimeOptions"
						:value="formData.mealTimeIndex"
						@change="onMealTimeChange"
					>
						<view class="form-input picker-input">
							{{ mealTimeOptions[formData.mealTimeIndex] || '请选择用餐时段' }}
						</view>
					</picker>
				</view>



				<view class="form-item">
					<view class="form-label">参与人数</view>
					<picker 
						:range="participantOptions" 
						:value="formData.participantIndex" 
						@change="onParticipantChange"
					>
						<view class="form-input picker-input">
							{{ participantOptions[formData.participantIndex] || '请选择用餐人数' }}
						</view>
					</picker>
				</view>



				<view class="form-item">
					<view class="form-label">备注说明</view>
					<textarea 
						class="form-textarea" 
						v-model="formData.remarks" 
						placeholder="其他需要说明的信息（可选）"
						maxlength="200"
					></textarea>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button 
					class="submit-btn" 
					@click="submitReservation"
					:disabled="submitting"
				>
					{{ submitting ? '提交中...' : '提交预约' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { createTastingReservation, getReservationList } from '@/config/api/reservation.js';
import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';

// 获取明天的日期作为默认预约日期
const getTomorrowDate = () => {
	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
	return tomorrow.toISOString().split('T')[0];
};

// 表单数据
const formData = ref({
	contactName: '',
	contactPhone: '',
	reservationDate: getTomorrowDate(), // 设置默认值为明天
	mealTimeIndex: 0,
	mealTime: '早餐', // 将在mealTimeConfig定义后更新
	participantIndex: 0,
	participantCount: 1,
	remarks: ''
});

// 用户信息
const userInfo = ref(null);

// 预约记录列表
const reservationList = ref([]);
const loading = ref(false);

// 参与人数选项
const participantOptions = ref(['1人', '2人', '3人', '4人', '5人', '6人以上']);

// 用餐时段配置（与meal.vue保持一致）
const mealTimeConfig = ref([
  { id: 'breakfast', name: '早餐', displayName: '早餐', cutoffHour: 7, cutoffMinute: 30 },
  { id: 'lunch', name: '中餐', displayName: '中餐', cutoffHour: 11, cutoffMinute: 0 },
  { id: 'dinner', name: '晚餐', displayName: '晚餐', cutoffHour: 16, cutoffMinute: 30 }
])

// 用餐时段选项（动态计算可用性）
const mealTimeOptions = computed(() => {
  return mealTimeConfig.value.map(meal => {
    const isAvailable = isMealTimeAvailableForDate(meal, formData.value.reservationDate)
    return isAvailable ? meal.displayName : `${meal.displayName}（需提前一天预约）`
  })
})

// 初始化表单数据中的用餐时段
formData.value.mealTime = mealTimeConfig.value[0].name

// 提交状态
const submitting = ref(false);

// 日期范围（需提前一天预约）
const minDate = computed(() => {
	const now = new Date();
	// 试餐预约需要提前一天，所以最早可选日期为明天
	const tomorrow = new Date(now);
	tomorrow.setDate(now.getDate() + 1);
	return tomorrow.toISOString().split('T')[0];
});

const maxDate = computed(() => {
	const today = new Date();
	// 设置为当年的最后一天（12月31日）
	const endOfYear = new Date(today.getFullYear(), 11, 31); // 月份从0开始，11表示12月
	return endOfYear.toISOString().split('T')[0];
});

// 检查用餐时段在特定日期是否可选（需提前一天预约）
const isMealTimeAvailableForDate = (mealTime, dateString) => {
  if (!dateString) return true // 如果没有选择日期，默认可选

  const now = new Date()
  const selectedDate = new Date(dateString)

  // 检查选择的日期是否是今天或之前
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const selected = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())

  // 如果选择的是今天或之前的日期，不可预约
  if (selected <= today) {
    return false
  }

  // 如果是明天及以后的日期，所有时段都可选
  return true
}

// 检查当前时间是否允许预约（需提前一天预约）
const checkTimeRestriction = () => {
  // 试餐预约需要提前一天，所以总是返回true（因为最小日期已经设为明天）
  // 这个函数保留是为了保持代码结构的一致性
  console.log('试餐预约需要提前一天，最早可预约明天')
  return true
}

// 日期选择
const onDateChange = (e) => {
	formData.value.reservationDate = e.detail.value;

	// 检查当前选择的用餐时段是否在新日期仍然可用
	const currentMealConfig = mealTimeConfig.value[formData.value.mealTimeIndex]
	if (currentMealConfig && !isMealTimeAvailableForDate(currentMealConfig, e.detail.value)) {
		// 如果当前选择的时段不可用，重置为第一个可用的时段
		const availableIndex = mealTimeConfig.value.findIndex(meal =>
			isMealTimeAvailableForDate(meal, e.detail.value)
		)
		if (availableIndex !== -1) {
			formData.value.mealTimeIndex = availableIndex
			formData.value.mealTime = mealTimeConfig.value[availableIndex].name
			uni.showToast({
				title: `已自动切换到${mealTimeConfig.value[availableIndex].displayName}`,
				icon: 'none'
			})
		} else {
			// 如果没有可用时段，说明选择的是今天或之前的日期
			console.warn('选择的日期需要提前一天预约')
			uni.showToast({
				title: '试餐需提前一天预约，请选择明天及以后的日期',
				icon: 'none'
			})
		}
	}
};

// 用餐时段选择
const onMealTimeChange = (e) => {
	const selectedIndex = e.detail.value
	const selectedMealConfig = mealTimeConfig.value[selectedIndex]

	// 检查选择的时段是否可用
	if (selectedMealConfig && !isMealTimeAvailableForDate(selectedMealConfig, formData.value.reservationDate)) {
		uni.showToast({
			title: '试餐需提前一天预约，请选择明天及以后的日期',
			icon: 'none'
		})
		return
	}

	formData.value.mealTimeIndex = selectedIndex;
	formData.value.mealTime = selectedMealConfig ? selectedMealConfig.name : mealTimeConfig.value[0].name;
};



// 参与人数选择
const onParticipantChange = (e) => {

	console.log(e.detail.value)
	formData.value.participantIndex = e.detail.value;
	formData.value.participantCount = Number(e.detail.value) + 1;
};

// 表单验证
const validateForm = () => {
	if (!formData.value.contactName.trim()) {
		uni.showToast({
			title: '请输入联系人姓名',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.contactPhone.trim()) {
		uni.showToast({
			title: '请输入联系电话',
			icon: 'none'
		});
		return false;
	}

	// 验证手机号格式
	const phoneRegex = /^1[3-9]\d{9}$/;
	if (!phoneRegex.test(formData.value.contactPhone)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.reservationDate) {
		uni.showToast({
			title: '请选择预约日期',
			icon: 'none'
		});
		return false;
	}

	// 检查选择的用餐时段是否可用
	const selectedMealConfig = mealTimeConfig.value[formData.value.mealTimeIndex]
	if (selectedMealConfig && !isMealTimeAvailableForDate(selectedMealConfig, formData.value.reservationDate)) {
		uni.showToast({
			title: '试餐需提前一天预约，请选择明天及以后的日期',
			icon: 'none'
		});
		return false;
	}

	return true;
};

// 提交预约
const submitReservation = async () => {
	if (!validateForm()) {
		return;
	}

	submitting.value = true;

	try {
		// 构造预约时间 - 根据用餐时段设置默认时间
		let defaultTime = '12:00'; // 默认中餐时间
		if (formData.value.mealTime === '早餐') {
			defaultTime = '08:00';
		} else if (formData.value.mealTime === '中餐') {
			defaultTime = '12:00';
		} else if (formData.value.mealTime === '晚餐') {
			defaultTime = '18:00';
		}

		const reservationDateTime = `${formData.value.reservationDate} ${defaultTime}:00`;

		// 构造扩展属性
		const extendedProperties = {
			mealTime: formData.value.mealTime,
			participantCount: formData.value.participantCount,
			remarks: formData.value.remarks
		};

		const requestData = {
			contactPhone: formData.value.contactPhone,
			userName: formData.value.contactName,
			reservationTime: reservationDateTime,
			description: `试餐预约 - ${formData.value.mealTime} - ${formData.value.participantCount}${formData.value.participantCount > 5 ? '人以上' : '人'}`,
			extendedProperties: JSON.stringify(extendedProperties)
		};

		console.log('提交试餐预约:', requestData);

		await createTastingReservation(requestData);

		uni.showToast({
			title: '预约成功',
			icon: 'success'
		});

		setTimeout(() => {
			// 重新加载预约列表
			loadReservationList();
		}, 500);

		

		// 页面返回顶部并重置表单
		setTimeout(() => {
			// 页面滚动到顶部
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 300
			});

			// 重置表单
			resetForm();
		}, 500);

	} catch (error) {
		console.error('提交试餐预约失败:', error);
		uni.showToast({
			title: error.message || '预约失败，请重试',
			icon: 'none'
		});
	} finally {
		submitting.value = false;
	}
};

// 分享给朋友
const onShareAppMessage = () => {
	return getShareAppMessageConfig({
		title: '东方爱堡月子会所 - 试餐预约',
		path: '/pages_business/pages/reservation/tasting',
	});
}

// 分享到朋友圈
const onShareTimeline = () => {
	return getShareTimelineConfig({
		title: '东方爱堡月子会所试餐预约，体验专业月子餐',
	});
}

// 获取用户信息
const getUserInfo = () => {
	try {
		const userInfoStr = uni.getStorageSync('userInfo');
		if (userInfoStr) {
			userInfo.value = JSON.parse(userInfoStr);
			// 自动填充用户信息
			if (userInfo.value.wxNickname) {
				formData.value.contactName = userInfo.value.wxNickname;
			}
			if (userInfo.value.phoneNumber) {
				formData.value.contactPhone = userInfo.value.phoneNumber;
			}
		}
	} catch (error) {
		console.error('获取用户信息失败:', error);
	}
};

// 加载预约记录列表
const loadReservationList = async () => {
	loading.value = true;

	try {
		const params = {
			reservationType: 2 // 试餐预约类型
		};

		console.log('加载试餐预约列表，参数:', params);

		const response = await getReservationList(params);

		if (response && Array.isArray(response)) {
			reservationList.value = response;
		} else {
			reservationList.value = [];
		}

		console.log('试餐预约列表加载成功:', reservationList.value);

	} catch (error) {
		console.error('加载试餐预约列表失败:', error);
		reservationList.value = [];
	} finally {
		loading.value = false;
	}
};

// 重置表单
const resetForm = () => {
	formData.value.reservationDate = getTomorrowDate(); // 重置时也使用明天作为默认值
	formData.value.mealTimeIndex = 0;
	formData.value.mealTime = mealTimeConfig.value[0].name; // 使用配置中的第一个时段
	formData.value.participantIndex = 0;
	formData.value.participantCount = 1;
	formData.value.remarks = '';
};

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		0: '待确认',
		1: '已确认',
		2: '已完成',
		3: '已取消'
	};
	return statusMap[status] || '未知状态';
};

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		0: 'status-pending',
		1: 'status-confirmed',
		2: 'status-completed',
		3: 'status-cancelled'
	};
	return classMap[status] || '';
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
	if (!dateTimeStr) return '';
	const date = new Date(dateTimeStr);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hour = String(date.getHours()).padStart(2, '0');
	const minute = String(date.getMinutes()).padStart(2, '0');
	return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 格式化日期（只显示日期，不显示时间）
const formatDate = (dateTimeStr) => {
	if (!dateTimeStr) return '';
	const date = new Date(dateTimeStr);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
};

// 获取扩展属性
const getExtendedProperty = (extendedPropertiesStr, key) => {
	try {
		if (!extendedPropertiesStr) return null;
		const properties = JSON.parse(extendedPropertiesStr);
		return properties[key];
	} catch (error) {
		console.error('解析扩展属性失败:', error);
		return null;
	}
};

onMounted(() => {
	console.log('试餐预约页面加载完成');

	// 提示用户试餐需要提前一天预约
	uni.showToast({
		title: '试餐需提前一天预约，最早可预约明天',
		icon: 'none',
		duration: 3000
	});

	getUserInfo();
	loadReservationList();
});
</script>

<style scoped>
.tasting-container {
	min-height: 100vh;
	background-color: #FAFAF5;
}

.tasting-content {
	padding: 20rpx;
}

.history-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.reservation-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.reservation-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 25rpx;
	border-left: 4rpx solid #ff6b6b;
}

.reservation-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.reservation-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.reservation-status {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	color: white;
}

.status-pending {
	background: #FF9800;
}

.status-confirmed {
	background: #4CAF50;
}

.status-completed {
	background: #9E9E9E;
}

.status-cancelled {
	background: #F44336;
}

.reservation-info {
	margin-bottom: 15rpx;
}

.info-row {
	display: flex;
	margin-bottom: 8rpx;
	font-size: 26rpx;
}

.info-label {
	color: #666;
	width: 160rpx;
}

.info-value {
	color: #333;
	flex: 1;
}

.reservation-description {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	padding: 15rpx;
	background: white;
	border-radius: 8rpx;
}

.intro-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.intro-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.intro-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.form-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	font-weight: 500;
}

.form-input, .picker-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.picker-input {
	display: flex;
	align-items: center;
	color: #999;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.submit-section {
	padding: 30rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: #8b5a2b;
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn:disabled {
	background: #ccc;
}
</style>
