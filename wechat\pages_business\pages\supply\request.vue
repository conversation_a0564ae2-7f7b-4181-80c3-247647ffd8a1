<template>
	<view class="supply-request-container">
		<custom-nav-bar title="用品补充需求"></custom-nav-bar>
		
		<view class="supply-request-content">
			<!-- 用品补充介绍 -->
			<view class="intro-section">
				<view class="intro-title">用品补充服务</view>
				<view class="intro-desc">
					我们提供贴心的用品补充服务，如果您在安心休养时（或：入住期间 / 休养调理期间）需要任何生活用品、护理用品或其他物品，请通过此页面提交需求，我们将尽快为您安排。
				</view>
			</view>

			<!-- 我的需求列表 -->
			<view class="my-requests-section" v-if="myRequests.length > 0">
				<view class="section-title">我的需求记录</view>
				<view class="request-list">
					<view 
						class="request-item" 
						v-for="(item, index) in myRequests" 
						:key="item.id"
					>
						<view class="request-header">
							<view class="request-title">用品补充需求</view>
							<view class="request-status" :class="getStatusClass(item.status)">
								{{ getStatusText(item.status) }}
							</view>
						</view>
						<view class="request-info">
							<view class="info-row">
								<text class="info-label">需求内容：</text>
								<text class="info-value">{{ item.requestContent }}</text>
							</view>

							<view class="info-row">
								<text class="info-label">提交时间：</text>
								<text class="info-value">{{ formatDateTime(item.createTime) }}</text>
							</view>
						</view>

					</view>
				</view>
			</view>

			<!-- 提交新需求表单 -->
			<view class="form-section">
				<view class="form-title">提交新需求</view>
				
				<view class="form-item">
					<view class="form-label">物品名称</view>
					<input 
						class="form-input" 
						v-model="formData.itemName" 
						placeholder="请输入需要的物品名称"
						maxlength="50"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">物品类型</view>
					<picker 
						:range="categoryOptions" 
						:value="formData.categoryIndex" 
						@change="onCategoryChange"
					>
						<view class="form-input picker-input">
							{{ categoryOptions[formData.categoryIndex] || '请选择物品类型' }}
						</view>
					</picker>
				</view>

				<view class="form-item">
					<view class="form-label">需要数量</view>
					<view class="quantity-input">
						<input 
							class="quantity-number" 
							v-model="formData.quantity" 
							placeholder="数量"
							type="number"
							min="1"
						/>
						<input 
							class="quantity-unit" 
							v-model="formData.unit" 
							placeholder="单位"
							maxlength="10"
						/>
					</view>
				</view>



				<view class="form-item">
					<view class="form-label">联系电话</view>
					<input 
						class="form-input" 
						v-model="formData.contactPhone" 
						placeholder="请输入联系电话"
						type="number"
						maxlength="11"
					/>
				</view>

				<view class="form-item">
					<view class="form-label">备注说明</view>
					<textarea 
						class="form-textarea" 
						v-model="formData.remarks" 
						placeholder="其他需要说明的信息（可选）"
						maxlength="200"
					></textarea>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button 
					class="submit-btn" 
					@click="submitRequest"
					:disabled="submitting"
				>
					{{ submitting ? '提交中...' : '提交需求' }}
				</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { listMemberCheckin } from "@/config/api/member_check_in.js";
import { submitSupplyRequest, getSupplyRequestList } from '@/config/api/supply.js';
import { getShareAppMessageConfig, getShareTimelineConfig } from '@/utils/share.js';


// 用户信息
const memberInfo = ref(null)

// 表单数据
const formData = ref({
	itemName: '',
	categoryIndex: 0,
	category: '生活用品',
	quantity: '',
	unit: '个',
	contactPhone: '',
	remarks: ''
});

// 用户信息
const userInfo = ref(null);

// 我的需求列表
const myRequests = ref([]);

// 选项数据
const categoryOptions = ref(['生活用品', '护理用品', '母婴用品', '食品饮料', '药品保健', '其他用品']);

// 提交状态
const submitting = ref(false);

// 状态映射
const statusMap = {
	0: '待处理',
	1: '处理中',
	2: '已完成',
	3: '已取消'
};

// 获取状态文本
const getStatusText = (status) => {
	return statusMap[status] || '未知状态';
};

// 获取状态样式类
const getStatusClass = (status) => {
	const classMap = {
		0: 'status-pending',
		1: 'status-processing',
		2: 'status-completed',
		3: 'status-cancelled'
	};
	return classMap[status] || '';
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
	if (!dateTimeStr) return '';
	const date = new Date(dateTimeStr);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hour = String(date.getHours()).padStart(2, '0');
	const minute = String(date.getMinutes()).padStart(2, '0');
	return `${year}-${month}-${day} ${hour}:${minute}`;
};

// 物品类型选择
const onCategoryChange = (e) => {
	formData.value.categoryIndex = e.detail.value;
	formData.value.category = categoryOptions.value[e.detail.value];
};



// 表单验证
const validateForm = () => {
	if (!formData.value.itemName.trim()) {
		uni.showToast({
			title: '请输入物品名称',
			icon: 'none'
		});
		return false;
	}

	if (!formData.value.quantity || formData.value.quantity <= 0) {
		uni.showToast({
			title: '请输入正确的数量',
			icon: 'none'
		});
		return false;
	}



	if (formData.value.contactPhone && formData.value.contactPhone.trim()) {
		// 验证手机号格式
		const phoneRegex = /^1[3-9]\d{9}$/;
		if (!phoneRegex.test(formData.value.contactPhone)) {
			uni.showToast({
				title: '请输入正确的手机号',
				icon: 'none'
			});
			return false;
		}
	}

	return true;
};

// 提交需求
const submitRequest = async () => {
	if (!validateForm()) {
		return;
	}

	if(memberInfo.value.status === 1){
		return uni.showToast({
			title: '您还未入住，请先办理入住',
			icon: 'none'
		});
	}

	submitting.value = true;

	try {
		// 构造需求内容描述
		const requestContent = `物品名称：${formData.value.itemName}，类型：${formData.value.category}，数量：${formData.value.quantity}${formData.value.unit}`;

		const requestData = {
			requestContent: requestContent
		};

		// 只有非空值才添加到请求数据中
		if (formData.value.contactPhone && formData.value.contactPhone.trim()) {
			requestData.userPhone = formData.value.contactPhone.trim();
		}

		if (formData.value.remarks && formData.value.remarks.trim()) {
			requestData.adminRemark = formData.value.remarks.trim();
		}

		console.log('提交用品补充需求:', requestData);

		await submitSupplyRequest(requestData);

		uni.showToast({
			title: '提交成功',
			icon: 'success'
		});

		// 重新加载我的需求列表

		setTimeout(() => {
			loadMyRequests();
		},500)

		// 页面返回顶部并重置表单
		setTimeout(() => {
			// 页面滚动到顶部
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 300
			});

			// 重置表单
			resetForm();
		}, 500);

	} catch (error) {
		console.error('提交用品补充需求失败:', error);

		// 详细的错误信息处理
		let errorMessage = '提交失败，请重试';
		if (error && error.message) {
			errorMessage = error.message;
		} else if (error && typeof error === 'string') {
			errorMessage = error;
		} else if (error && error.data && error.data.message) {
			errorMessage = error.data.message;
		}

		console.log('错误详情:', {
			error: error,
			requestData: {
				requestContent: `物品名称：${formData.value.itemName}，类型：${formData.value.category}，数量：${formData.value.quantity}${formData.value.unit}`,
				userPhone: formData.value.contactPhone || null,
				adminRemark: formData.value.remarks || null
			}
		});

		uni.showToast({
			title: errorMessage,
			icon: 'none',
			duration: 3000
		});
	} finally {
		submitting.value = false;
	}
};

// 重置表单
const resetForm = () => {
	formData.value = {
		itemName: '',
		categoryIndex: 0,
		category: '生活用品',
		quantity: '',
		unit: '个',
		contactPhone: '',
		remarks: ''
	};
};

// 加载我的需求列表
const loadMyRequests = async () => {
	try {
		const params = {
			pageNum: 1,
			pageSize: 10
		};

		console.log('加载我的用品补充需求列表');

		const response = await getSupplyRequestList(params);

		console.log('API响应数据:', response);

		if (response && Array.isArray(response)) {
			myRequests.value = response;
		} else {
			myRequests.value = [];
		}

		console.log('我的需求列表加载成功:', myRequests.value);

	} catch (error) {
		console.error('加载我的需求列表失败:', error);
		myRequests.value = [];
	}
};

// 分享给朋友
const onShareAppMessage = () => {
	return getShareAppMessageConfig({
		title: '东方爱堡月子会所 - 用品补充需求',
		path: '/pages_business/pages/supply/request',
	});
}

// 分享到朋友圈
const onShareTimeline = () => {
	return getShareTimelineConfig({
		title: '东方爱堡月子会所用品补充服务，贴心满足您的需求',
	});
}

// 获取用户信息
const getUserInfo = () => {
	try {
		const userInfoStr = uni.getStorageSync('userInfo');
		if (userInfoStr) {
			userInfo.value = JSON.parse(userInfoStr);
			// 自动填充用户信息
			if (userInfo.value.phoneNumber) {
				formData.value.contactPhone = userInfo.value.phoneNumber;
			}
		}
	} catch (error) {
		console.error('获取用户信息失败:', error);
	}
};



const fetchMemberInfo = async () => {
	try {
		const response = await listMemberCheckin()
		console.log(response);
		
		if (response) {
			memberInfo.value = response
			uni.setStorageSync('memberInfo', JSON.stringify(response));
		} else {
			console.warn('用户信息获取失败，响应为空')
		}
	} catch (error) {
		console.error('获取用户信息失败:', error)
		uni.showToast({
			title: '获取用户信息失败',
			icon: 'none'
		})
	}
}

onMounted(() => {
	console.log('用品补充需求页面加载完成');
	getUserInfo();
	loadMyRequests();
	fetchMemberInfo()
});
</script>

<style scoped>
.supply-request-container {
	min-height: 100vh;
	background-color: #FAFAF5;
}

.supply-request-content {
	padding: 20rpx;
}

.intro-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.intro-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.intro-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.my-requests-section {
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	padding: 0 10rpx;
}

.request-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.request-item {
	background: white;
	border-radius: 12rpx;
	padding: 25rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.request-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.request-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.request-status {
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	color: white;
}

.status-pending {
	background: #FF9800;
}

.status-processing {
	background: #2196F3;
}

.status-completed {
	background: #4CAF50;
}

.status-cancelled {
	background: #F44336;
}

.request-info {
	margin-bottom: 15rpx;
}

.info-row {
	display: flex;
	margin-bottom: 8rpx;
	font-size: 24rpx;
}

.info-label {
	color: #666;
	width: 140rpx;
}

.info-value {
	color: #333;
	flex: 1;
}

.request-description {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	padding: 15rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
}

.form-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.form-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	font-weight: 500;
}

.form-input, .picker-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.picker-input {
	display: flex;
	align-items: center;
	color: #999;
}

.quantity-input {
	display: flex;
	gap: 20rpx;
}

.quantity-number {
	flex: 2;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.quantity-unit {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.submit-section {
	padding: 30rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: #8b5a2b;
	color: white;
	border: none;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submit-btn:disabled {
	background: #ccc;
}
</style>
