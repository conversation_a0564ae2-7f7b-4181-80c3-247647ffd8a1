<template>
  <view class="container">
    <!-- 背景图片 -->
    <image class="background-image" src="https://xiayuxinzhu.cn/test/api/files/previewResized/1951689046962995202/larger" mode="aspectFill"></image>
    <!-- <image class="background-image" :src="getLargeImageUrl('1951689046962995202')" mode="aspectFill"></image> -->

    <custom-nav-bar title="抽奖大转盘" :showBack="isShowBac" :showBackInfo="!isShowBack"></custom-nav-bar>
    <!-- 大转盘 -->
    <view class="dial">
      <!-- <image class="arrow" src="/assets/background/pin          _arrow.png"></image> -->
      <image
        class="bg"
        :style="`transform: rotate(${degree}deg);transition: transform ${duration}s`"
        src="https://xiayuxinzhu.cn/test/api/files/previewResized/1951689449653927938/larger"
      />
      <!-- <image
        class="bg"
        :style="`transform: rotate(${degree}deg);transition: transform ${duration}s`"
        :src="getLargeImageUrl('1951689449653927938')"
      /> -->
      <image class="go" @tap="handleTurn" src="https://xiayuxinzhu.cn/test/api/files/previewResized/1951691586203025410/small"></image>
      <!-- <image class="go" @tap="handleTurn" :src="getLargeImageUrl('1951691586203025410')"></image> -->
    </view>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref, onMounted } from 'vue'
import { listMemberCheckin,updateMemberInfo } from "@/config/api/member_check_in.js";
import {getLargeImageUrl} from '@/config/api/image.js';

// 响应式数据
const degree = ref(0) // 转盘旋转角度
const duration = ref(0) // 动画持续时间
const isSpinning = ref(false) // 是否正在旋转
const isShowBack = ref(true) 


// 用户信息
const memberInfo = ref(null)

// 奖品配置（根据实际转盘图片的角度分布）
const prizes = ref([
  { name: '家属餐一份', probability: 70, startAngle: 0, endAngle: 252 },      // 0°~252° (252度范围)
  { name: '尿不湿', probability: 5, startAngle: 252, endAngle: 270 },      // 252°~270° (18度范围)
  { name: '产康次数', probability: 10, startAngle: 270, endAngle: 306 },   // 270°~306° (36度范围)
  { name: '续住一天', probability: 5, startAngle: 306, endAngle: 324 },    // 306°~324° (18度范围)
  { name: '婴儿游泳', probability: 10, startAngle: 324, endAngle: 360 }    // 324°~360° (36度范围)
])

// 当前中奖结果
const currentPrize = ref(null)

// 获取用户信息
const fetchMemberInfo = async () => {
  try {
    const response = await listMemberCheckin()
    console.log(response);
    
    if (response) {
      memberInfo.value = response
    } else {
      console.warn('用户信息获取失败，响应为空')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    })
  }
}

// 根据最终角度判断指针实际指向哪个奖品（用于调试）
const getPrizeByAngle = (finalAngle) => {
  // 确保角度为正数并标准化到0-360度范围
  let normalizedAngle = ((finalAngle % 360) + 360) % 360

  // 关键修正：转盘旋转后，我们需要计算指针相对于转盘的位置
  // 指针固定在0度，转盘旋转了normalizedAngle度
  // 所以指针相对于转盘的位置是 (360 - normalizedAngle) % 360
  const pointerRelativeAngle = (360 - normalizedAngle) % 360

  for (let i = 0; i < prizes.value.length; i++) {
    const prize = prizes.value[i]
    const startAngle = prize.startAngle
    const endAngle = prize.endAngle

    if (endAngle > startAngle) {
      // 正常情况，不跨越0度
      if (pointerRelativeAngle >= startAngle && pointerRelativeAngle < endAngle) {
        return prize
      }
    } else {
      // 跨越0度的情况（如324°~360°，实际是324°~0°）
      if (pointerRelativeAngle >= startAngle || pointerRelativeAngle < endAngle) {
        return prize
      }
    }
  }

  // 如果没有找到匹配的奖品，返回第一个奖品作为默认值
  return prizes.value[0]
}

// 根据概率计算中奖结果
const calculatePrize = () => {
  const random = Math.random() * 100 // 0-100的随机数
  let cumulativeProbability = 0

  for (const prize of prizes.value) {
    cumulativeProbability += prize.probability
    if (random <= cumulativeProbability) {
      return prize
    }
  }

  // 默认返回第一个奖品（家属餐）
  return prizes.value[0]
}

// 计算转盘应该停止的角度
const calculateStopAngle = (targetPrize) => {
  // 指针指向上方正中间（0度），需要计算转盘应该转到的角度
  const startAngle = targetPrize.startAngle
  const endAngle = targetPrize.endAngle

  // 在奖品区域内随机选择一个角度，而不是总是选择中心
  let randomAngleInPrize
  if (endAngle > startAngle) {
    // 正常情况，不跨越0度
    // 在区域内随机选择，但避免边界（留出一些边距）
    const margin = Math.min(5, (endAngle - startAngle) * 0.1) // 边距为区域大小的10%，最大5度
    const adjustedStart = startAngle + margin
    const adjustedEnd = endAngle - margin
    randomAngleInPrize = adjustedStart + Math.random() * (adjustedEnd - adjustedStart)
  } else {
    // 跨越0度的情况（如324°~360°实际是324°~0°）
    const totalRange = (360 - startAngle) + endAngle
    const margin = Math.min(5, totalRange * 0.1)
    const randomInRange = margin + Math.random() * (totalRange - 2 * margin)

    if (randomInRange <= (360 - startAngle)) {
      randomAngleInPrize = startAngle + randomInRange
    } else {
      randomAngleInPrize = randomInRange - (360 - startAngle)
    }
  }

  // 转盘需要转动的角度，让随机选择的角度对准指针（0度位置）
  let stopAngle = (360 - randomAngleInPrize) % 360

  return stopAngle
}

// 点击转盘按钮
const handleTurn = () => {
  // 如果正在旋转，忽略点击
  if (isSpinning.value) {
    return
  }

  // 计算中奖结果
  const winPrize = calculatePrize()
  currentPrize.value = winPrize

  // 计算停止角度（指向奖品正中间）
  const stopAngle = calculateStopAngle(winPrize)

  // 生成随机转动角度（超过1000度）
  const minRotation = 1000 // 最少转动1000度
  const maxRotation = 3600 // 最多转动3600度（10圈）
  const randomRotation = Math.random() * (maxRotation - minRotation) + minRotation

  // 开始旋转
  isSpinning.value = true

  // 设置旋转参数
  const spinDuration = 4 // 旋转持续时间（秒）
  const totalRotation = randomRotation + stopAngle

  // 设置动画时间和角度
  duration.value = spinDuration
  degree.value = degree.value + totalRotation

  // 旋转结束后显示结果
  setTimeout(() => {
    isSpinning.value = false
    duration.value = 0

    // 调试：检查指针实际指向哪个奖品
    const actualPrize = getPrizeByAngle(degree.value)

    // 详细调试：测试当前角度附近的几个角度
    const testAngle = degree.value % 360
    for (let offset = -10; offset <= 10; offset += 5) {
      const testAngleWithOffset = (testAngle + offset + 360) % 360
      const testPrize = getPrizeByAngle(testAngleWithOffset)
    }

    // 修正：使用实际指向的奖品作为最终结果
    if (actualPrize) {
      currentPrize.value = actualPrize
    }

    // 显示中奖结果弹窗
    showPrizeResult()
  }, spinDuration * 1000)
}



// 更新会员奖品信息
const updatePrizeInfo = async (prize) => {
  try {
    console.log('开始更新会员奖品信息...')
    console.log('当前会员信息:', memberInfo.value)
    console.log('中奖奖品:', prize)

    // 如果没有会员信息，创建一个基本的更新数据
    const updateData = {
      ...(memberInfo.value || {}),
      prizeContent: prize.name,
      prizeTime: new Date().toISOString()
    }

    console.log('准备更新的数据:', updateData)
    const response = await updateMemberInfo(updateData)
    console.log('更新响应:', response)

    if (response) {
      console.log('奖品信息更新成功:', response)
      return true
    } else {
      console.error('奖品信息更新失败:', response)
      return false
    }
  } catch (error) {
    console.error('更新奖品信息时发生错误:', error)
    return false
  }
}

// 显示中奖结果弹窗
const showPrizeResult = async () => {
  console.log('准备显示中奖弹窗，当前奖品:', currentPrize.value)

  if (!currentPrize.value) {
    console.error('没有中奖奖品信息，无法显示弹窗')
    return
  }

  // 先更新奖品信息
  console.log('开始更新奖品信息...')
  const updateSuccess = await updatePrizeInfo(currentPrize.value)
  console.log('奖品信息更新结果:', updateSuccess)

  if (!updateSuccess) {
    console.log('奖品信息保存失败')
    uni.showToast({
      title: '奖品信息保存失败',
      icon: 'none'
    })
  }

  // 显示中奖弹窗
  uni.showModal({
    title: '恭喜中奖！',
    content: `您获得了：${currentPrize.value.name}`,
    showCancel: false,
    confirmText:'查看奖品',
    success: (res) => {
      if (res.confirm) {
        console.log('用户确认中奖结果，跳转到我的奖品页面')
        // 跳转到我的奖品页面
        uni.navigateTo({
          url: '/pages_business/pages/my/prize?isShowBack=false'
        })
      }
    }
  })
}

onLoad((options) => {
  if(options.isShowBack) isShowBack.value = options.isShowBack === 'false' ? false : true
})

// 页面加载时获取用户信息
onMounted(() => {
  fetchMemberInfo()
})
</script>

<style lang="scss" scoped>
/* pages/shop/dial/index.wxss */

.container {
  width: 100vw;
  height: 100vh;
  padding-top:15vh ;
  position: relative;
}

.background-image {
  position: absolute;
  top: -75rpx;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.tip {
  margin: 210rpx 0 50rpx;
  color: #fff;
  font-size: 28rpx;
}

.tip text {
  color: #fa3c38;
}

/* dial */

.dial {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;

  
}

.dial .bg {
  width: 700rpx;
  height: 700rpx;
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 先快后慢，慢慢停下来 */
}

.dial .arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%,-50%);
  width: 64rpx;
  z-index: 100;
}

.dial .go {
  position: absolute;
  margin: auto;
  width: 120rpx;
  height: 140rpx;
  z-index: 100;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.dial .go:hover {
  transform: scale(1.05);
}

.dial .go:active {
  transform: scale(0.95);
}

.dial .go .info {
  position: absolute;
  top: 0;
  display: grid;
  place-content: center;
  place-items: center;
  width: 160rpx;
  height: 160rpx;
  color: #fff;
}

.dial .go .info .title {
  font-size: 44rpx;
}

.dial .go .info .cost {
  font-size: 20rpx;
}



/* 活动中奖 */
.rule-wrap {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content {
  display: flex;
  width: 640rpx;
  margin-top: 20vh;
  padding: 40rpx;
  color: #333333;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  flex-direction: column;
  align-items: center;
}

.rule-wrap .rule-content .image {
  width: 236rpx;
  height: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize {
  width: 236rpx;
  margin: 60rpx 0 0;
}

.rule-wrap .rule-content .prize image {
  width: 236rpx;
  height: 236rpx;
}

.rule-wrap .rule-content .prize .level {
  top: 180rpx;
  width: 236rpx;
  height: 56rpx;
  line-height: 56rpx;
}

.rule-wrap .rule-content .text {
  margin: 40rpx 0 20rpx;
  color: #333;
  font-size: 28rpx;
  text-align: center;
}

.rule-wrap .rule-content button-common {
  width: calc(100% - 15rpx);
  margin: 15rpx 0 15rpx;
}

.rule-wrap .rule-content .common {
  height: 90rpx;
  border-radius: 50rpx;
  background: linear-gradient(180deg, #ffdbad 0%, #faa638 100%);
  color: #6a3c00;
  font-size: 28rpx;
}

.rule-wrap .rule-content .light {
  background: #fff;
  border: 2rpx solid #ccc;
  color: #666;
}

.rule-wrap .rule-close {
  position: absolute;
  right: 54rpx;
  top: calc(20vh - 120rpx);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, .6);
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  font-size: 36rpx;
}




</style>