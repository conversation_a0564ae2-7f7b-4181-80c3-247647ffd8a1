// 全局请求封装
const base_url = 'https://xiayuxinzhu.cn/test'
// 需要修改token，和根据实际修改请求头
export default (params) => {
	let url = params.url;
	let method = params.method || "get";
	let data = params.data || {};
	let header = {}
	let showError = params.showError !== false; // 默认显示错误
	if (method == "post" || method === "put") {
		header = {
			'Content-Type': 'application/json'
		};
	}
	// 获取本地token
	if (uni.getStorageSync("token")) {
		header['Authorization'] = 'Bearer ' + uni.getStorageSync("token");
	}
 
	return new Promise((resolve, reject) => {
		uni.request({
			url: base_url + url,
			method: method,
			header: header,
			data: data,
			success(response) {
				const res = response
				// 根据返回的状态码做出对应的操作
				//获取成功
				if (res.statusCode == 200) {
					resolve(res.data);
				} else {
					// uni.clearStorageSync()
					let errorMsg = '';
					
					// 尝试从响应中提取错误信息
					if (res.data && typeof res.data === 'string') {
						errorMsg = res.data;
					} else if (res.data && res.data.message) {
						errorMsg = res.data.message;
					} else if (res.data && res.data.error) {
						errorMsg = res.data.error;
					} else if (res.data) {
						errorMsg = JSON.stringify(res.data);
					}
					
					console.error('请求失败:', {
						statusCode: res.statusCode,
						errorMsg: errorMsg,
						url: url,
						response: res
					});
					
					switch (res.statusCode) {
						case 401:
							uni.showModal({
								title: "提示",
								content: "请登录",
								showCancel: false,
								success(res) {
									setTimeout(() => {
										uni.navigateTo({
											url: "/pages/login/index",
										})
									}, 1000);
								},
							});
							break;
						case 404:
							if (showError) {
								uni.showToast({
									title: '请求地址不存在...',
									icon: 'none',
									duration: 2000,
								});
							}
							break;
						case 400:
							// 业务错误，可能包含具体错误信息
							if (showError && errorMsg) {
								uni.showToast({
									title: errorMsg,
									icon: 'none',
									duration: 2000,
								});
							}
							break;
						default:
							if (showError) {
								uni.showToast({
									title: errorMsg || '请求失败，请重试',
									icon: 'none',
									duration: 2000,
								});
							}
							break;
					}
					
					// 将错误信息传递给调用者
					reject({ 
						statusCode: res.statusCode,
						message: errorMsg,
						data: res.data,
						response: res
					});
				}
			},
			fail(err) {
				console.error('网络请求失败:', err);
				
				if (showError) {
					if (err.errMsg && err.errMsg.indexOf('request:fail') !== -1) {
						uni.showToast({
							title: '网络异常',
							icon: "error",
							duration: 2000
						});
					} else {
						uni.showToast({
							title: err.errMsg || '未知异常',
							icon: 'none',
							duration: 2000
						});
					}
				}
				
				reject(err);
			},
			complete() {
				// 不管成功还是失败都会执行
				try {
					uni.hideLoading();
				} catch (e) {
					console.warn('hideLoading failed:', e);
				}
				
				try {
					uni.hideToast();
				} catch (e) {
					console.warn('hideToast failed:', e);
				}
			}
		});
	}).catch((e) => {
		console.error('请求异常:', e);
		return Promise.reject(e);
	});
};